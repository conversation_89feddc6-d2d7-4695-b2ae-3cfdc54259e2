/* eslint-disable prettier/prettier */
import type {
  OrderHistoryDetailItemType,
  OrderHistoryDetailPromotionItemType,
  OrderHistoryPromotion,
  OrderHistoryPromotionAppliedBenefit,
  PromotionGroup,
  VendorPromotionGroups,
} from '../types';

export const getVendorPromotions = ({
  id,
  promotions,
}: {
  id: string;
  promotions: OrderHistoryPromotion[];
}) => {
  if (!promotions) return [];
  return promotions.filter((promotion) => promotion.vendor?.id === id);
};

const getMinimumQuantity = (promotion: OrderHistoryPromotion) => {
  const minimumQuantityCondition = promotion.appliedRules
    .flatMap((rule) => rule.conditions)
    .find((condition) => condition.type === 'minimum_quantity');

  return minimumQuantityCondition
    ? +minimumQuantityCondition.config.quantity
    : 1;
};

export const getItemDetails = ({
  item,
  promotion,
  freeBenefit,
}: {
  item: OrderHistoryDetailItemType;
  promotion: OrderHistoryPromotion;
  freeBenefit: OrderHistoryPromotionAppliedBenefit;
}) => {
  const triggerMinQty = getMinimumQuantity(promotion);
  const triggeredTimes = Math.floor(item.quantity / triggerMinQty);
  const freeQtyPerTrigger = +freeBenefit.quantity;
  const freeItemsQty = triggeredTimes * freeQtyPerTrigger;

  return {
    subtotalPaidItems: +item.totalPrice,
    subtotalAllItems: +item.totalPrice + (freeItemsQty * +item.unitPrice),
    paidItemsQty: item.quantity,
    freeItemsQty,
    freeOffer: freeBenefit.freeOffer,
  };
};
const offerIds = ({ productOfferId }: { productOfferId: string }) =>
  productOfferId;
type GroupBuyXGetYItemsProps = {
  items: OrderHistoryDetailItemType[];
  promotions: OrderHistoryPromotion[];
};
export function groupBuyXGetYItems({
  items,
  promotions,
}: GroupBuyXGetYItemsProps): VendorPromotionGroups {
  const buyXGetYPromotion = promotions.find(
    (promo) => promo.type === 'buy_x_get_y',
  );

  const triggeredOfferIds = buyXGetYPromotion?.triggeringItems.map(offerIds);

  if (
    !buyXGetYPromotion ||
    !triggeredOfferIds ||
    triggeredOfferIds.length === 0
  )
    return {
      triggeredItems: null,
      untriggeredItems: items,
    };

  const { triggered, untriggered } = items.reduce(
    (acc, item) => {
      const itemsType = triggeredOfferIds.includes(item?.productOfferId)
        ? 'triggered'
        : 'untriggered';
      return {
        ...acc,
        [itemsType]: [...acc[itemsType], item],
      };
    },
    {
      triggered: [] as OrderHistoryDetailItemType[],
      untriggered: [] as OrderHistoryDetailItemType[],
    },
  );

  const freeBenefit = buyXGetYPromotion.appliedBenefits.find(
    (benefit) => benefit.type === 'give_free_product',
  );

  if (!freeBenefit || !triggered || triggered.length === 0)
    return {
      triggeredItems: null,
      untriggeredItems: items,
    };

  const triggeredItems = {
    ...triggered.reduce(
      (acc, item) => {
        if (!buyXGetYPromotion) return acc;
        const detail = getItemDetails({
          item,
          promotion: buyXGetYPromotion,
          freeBenefit,
        });
        return {
          subtotalPaidItems: acc.subtotalPaidItems + detail.subtotalPaidItems,
          subtotalAllItems: acc.subtotalAllItems + detail.subtotalAllItems,
          paidItemsQty: acc.paidItemsQty + detail.paidItemsQty,
          freeItemsQty: acc.freeItemsQty + detail.freeItemsQty,
          items: [
            ...acc.items,
            {
              ...item,
              freeItemsQty: detail.freeItemsQty,
              freeOffer: detail.freeOffer,
            },
          ],
        };
      },
      {
        items: [] as OrderHistoryDetailItemType[],
        subtotalPaidItems: 0,
        subtotalAllItems: 0,
        paidItemsQty: 0,
        freeItemsQty: 0,
      },
    ),
    promotion: buyXGetYPromotion,
  };

  return {
    triggeredItems,
    untriggeredItems: untriggered,
  };
}


type TriggeredItem = {
  items: OrderHistoryDetailPromotionItemType[];
  subtotalPaidItems: number;
  subtotalAllItems: number;
  paidItemsQty: number;
  freeItemsQty: number;
}
export const groupBXGY = ({ items, promotion }: { items: OrderHistoryDetailItemType[]; promotion: OrderHistoryPromotion; }) => {
  const triggeringIds = promotion.triggeringItems.map(offerIds);
  const triggeringItems = items.filter((item) =>  triggeringIds.includes(item.productOfferId));
  const freeBenefit = promotion.appliedBenefits.find(
    (benefit) => benefit.type === 'give_free_product',
  ) as OrderHistoryPromotionAppliedBenefit;

  return triggeringItems.reduce((acc, item) => {
    const detail = getItemDetails({ item, promotion, freeBenefit });

    return {
      subtotalPaidItems: acc.subtotalPaidItems + detail.subtotalPaidItems,
      subtotalAllItems: acc.subtotalAllItems + detail.subtotalAllItems,
      paidItemsQty: acc.paidItemsQty + detail.paidItemsQty,
      freeItemsQty: acc.freeItemsQty + detail.freeItemsQty,
      promotion,
      items: [
        ...acc.items,
        {
          ...item,
          freeItemsQty: detail.freeItemsQty,
          freeOffer: detail.freeOffer ?? null,
        },
      ],
    };
  },
  {
    items: [] as OrderHistoryDetailPromotionItemType[],
    subtotalPaidItems: 0,
    subtotalAllItems: 0,
    paidItemsQty: 0,
    freeItemsQty: 0,
  } as TriggeredItem,
  )
}

const promotionsSetter = {
  buy_x_get_y: groupBXGY,
};

const untriggeredInAllPromotions = ({
  triggeredItems, items
}: { triggeredItems: TriggeredItem[], items: OrderHistoryDetailItemType[] }) => {
    const flattenedTriggered = triggeredItems.flatMap(item => item.items);
    const flattenedTriggeredIds = new Set(flattenedTriggered.map(item => item.id));
    return items.filter(item => !flattenedTriggeredIds.has(item.id));
}

export const groupByPromotions = ({ items, promotions }: GroupBuyXGetYItemsProps) => {
  return Object.entries(promotionsSetter).reduce(
    (acc, [promotionType, setter]) => {
      const promotionsByType = promotions.filter(
        (promotion) => promotion.type === promotionType,
      );
      if (!promotionsByType) return acc;
      //TODO the untriggered items should be the ones not triggered
      // in all promotions, in this logic, it the untriggeredItems
      // will be overwritten by the next promotion
      const triggeredItems = promotionsByType.map((promotion) => setter({ items, promotion }))
      return {
        ...acc,
        untriggeredItems: untriggeredInAllPromotions({ triggeredItems, items }),
        [promotionType]: triggeredItems,
      };
    },
    {} as VendorPromotionGroups,
  );
};
