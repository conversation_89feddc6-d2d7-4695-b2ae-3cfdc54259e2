import { getPriceString } from '@/utils';
import type { PromotionGroup } from '@/libs/orders/types';
import { getProductOfferComputedData } from '@/libs/products/utils/getProductComputedData';
import { Link } from 'react-router-dom';
import { getProductUrl } from '@/apps/shop/routes/utils';
import { OrderStatus } from '../../../../OrderStatus/OrderStatus';

export const PromotionItemContent = ({
  promotionGroup,
}: {
  promotionGroup: PromotionGroup;
}) => {
  const {
    promotion,
    items,
    paidItemsQty,
    freeItemsQty,
    subtotalPaidItems,
    subtotalAllItems,
  } = promotionGroup;

  const totalItems = paidItemsQty + freeItemsQty;
  const firstItem = items[0];

  const allItems = items.flatMap((item) => {
    const itemsToShow = [item];

    if (item.freeItemsQty && item.freeItemsQty > 0 && item.freeOffer) {
      itemsToShow.push({
        ...item,
        id: `${item.id}-free`,
        quantity: item.freeItemsQty,
        unitPrice:
          getProductOfferComputedData(item.freeOffer)?.salePrice?.toString() ||
          '0',
        totalPrice: undefined,
        productOfferId: item.freeOffer.id,
        product: {
          ...item.product,
          name: item.freeOffer.name || item.product.name,
        },
        status: undefined,
      });
    }

    return itemsToShow;
  });

  return (
    <div className="flex">
      <div className="ml-4 flex-1">
        <div className="mb-2">
          <div className="flex">
            <p className="mb-0.5 text-xs font-medium text-gray-600/70">
              Order ID: {firstItem.orderNumber}
            </p>
            <div className="divider-v"></div>
            <p className="mb-1 text-xs font-medium text-green-700">Promotion</p>
          </div>
          <h3 className="mb-3 text-base font-medium text-gray-900">
            {promotion.name}
          </h3>
        </div>

        <div className="mt-2 flex items-center">
          <p className="min-w-[6rem] pr-3 text-xs text-gray-600">
            Quantity:{' '}
            <span className="font-bold text-gray-800">{totalItems}</span>
          </p>
          <div className="divider-v"></div>
          <p className="min-w-[6rem] pr-3 text-xs text-gray-600">
            Price:{' '}
            <span className="font-bold text-gray-800">
              {getPriceString(subtotalAllItems)}
            </span>
          </p>
          <div className="divider-v" />
          <p className="min-w-[6rem] pr-3 text-xs text-gray-600">
            Net Total:{' '}
            <span className="font-bold text-gray-800">
              {getPriceString(subtotalPaidItems)}
            </span>
          </p>
        </div>
        <div className="divider-h mt-4"></div>

        <div>
          {allItems.map((item) => (
            <div key={item.id} className="flex pt-4">
              <div className="grid w-full border-b border-gray-200 pb-4 last:border-none">
                <Link
                  to={getProductUrl(item.product.id, item.productOfferId)}
                  className="mb-2 no-underline hover:underline"
                >
                  <p className="font-medium text-gray-800">
                    {item.product.name}
                  </p>
                </Link>
                <div className="flex items-center">
                  <p className="min-w-[6rem] pr-2 text-xs text-gray-600">
                    Quantity:{' '}
                    <span className="font-bold text-gray-800">
                      {item.quantity}
                    </span>
                  </p>
                  <div className="divider-v"></div>
                  <p className="text-xs text-gray-600">
                    Price:{' '}
                    <span className="font-bold text-gray-800">
                      {getPriceString(item.unitPrice)}
                    </span>
                  </p>
                  <div className="divider-v"></div>
                  <p className="text-xs text-gray-600">
                    Net Total:{' '}
                    {item.totalPrice ? (
                      <span className="font-bold text-gray-800">
                        {getPriceString(item.totalPrice)}
                      </span>
                    ) : (
                      <span className="font-bold text-green-700">Free</span>
                    )}
                  </p>
                </div>
              </div>
              {!!item.status && (
                <div className="min-w-36">
                  <p className="mb-1 text-right text-xs font-medium text-gray-500/70">
                    Status
                  </p>
                  <OrderStatus status={item.status} align="right" />
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
