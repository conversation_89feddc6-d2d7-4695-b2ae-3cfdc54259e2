import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { Image, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import defaultProductImgUrl from '@/assets/images/default-product.png';
import { getPriceString } from '@/utils';
import type {
  OrderHistoryDetailItemType,
  OrderHistoryDetailVendorOrderType,
  OrderHistoryPromotion,
} from '@/libs/orders/types';
import { groupByPromotions } from '@/libs/orders/utils/promotionGrouping';
import { OrderHistoryRegularItem } from './components/OrderHistoryRegularItem/OrderHistoryRegularItem';
import { OrderHistoryPromoItem } from './components/OrderHistoryPromoItem/OrderHistoryPromoItem';

interface OrderVendorPanelProps {
  vendor: OrderHistoryDetailVendorOrderType['vendor'];
  items: OrderHistoryDetailItemType[];
  totalPrice: number;
  totalItems: number;
  promotions: OrderHistoryPromotion[] | null;
}

export const OrderVendorPanel = ({
  vendor,
  totalPrice,
  totalItems,
  items,
  promotions,
}: OrderVendorPanelProps) => {
  const groupedItems =
    !promotions || promotions.length === 0
      ? {
          untriggeredItems: items,
        }
      : groupByPromotions({
          items,
          promotions,
        });

  return (
    <CollapsiblePanel
      startOpen
      header={
        <Flex align="center" pr="5rem">
          <Image
            src={vendor.imageUrl}
            alt={vendor.name}
            fallbackSrc={defaultProductImgUrl}
            h={42}
          />
          <div className="ml-4">
            <Text c="#333" size="md" fw="500">
              {getPriceString(totalPrice)}
              <Text c="#666" size="xs" span ml="0.2rem">
                ({totalItems} Items)
              </Text>
            </Text>
          </div>
        </Flex>
      }
      content={
        <Flex p="md" direction="column">
          {/* {groupedItems.untriggeredItems.map((item) => (
            <OrderHistoryRegularItem key={item.id} item={item} />
          ))} */}
          <div className="divider-h my-2"></div>
          {groupedItems?.buy_x_get_y &&
            groupedItems.buy_x_get_y.map((promotionGroup) => (
              <OrderHistoryPromoItem
                key={promotionGroup.items[0].id}
                promotionGroup={promotionGroup}
              />
            ))}
        </Flex>
      }
    />
  );
};
